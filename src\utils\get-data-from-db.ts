import { cache } from "react"
import { unstable_cache } from "next/cache"
import { notFound } from "next/navigation"
import { verifySession } from "@/auth/dal"
import { EntityType, SiteSettings } from "@prisma/client"

import prisma from "@/lib/prisma"

import { dataLimits } from "./siteConfig"

export const getTestimonials = async () => {
  return await prisma.testimony.findMany()
}

export const getHomeCourses = async () => {
  return await prisma.course.findMany({
    where: { previewInHomePage: true },
    orderBy: { createdAt: "desc" },
  })
}

export const getCourses = async () => {
  return await prisma.course.findMany({ orderBy: { createdAt: "desc" } })
}

export const getCoursesWithLectures = async () => {
  return await prisma.course.findMany({
    orderBy: { createdAt: "desc" },
    include: { lectures: { orderBy: { createdAt: "asc" } } },
  })
}

export const getCourseById = cache(
  async ({ courseId }: { courseId: string }) => {
    return await prisma.course.findUnique({
      where: { id: courseId },
      include: { lectures: { orderBy: { createdAt: "asc" } } },
    })
  }
)

export const getCourseLectures = cache(
  async ({ courseId }: { courseId: string }) => {
    return await prisma.lecture.findMany({
      orderBy: { createdAt: "desc" },
      where: { courseId },
    })
  }
)

export const getComments = async ({
  page,
  entity,
  entityId,
}: {
  page: number
  entityId: string
  entity: EntityType
}) => {
  const limit = dataLimits.comments
  const skip = (page - 1) * limit

  const [data, totalCount] = await prisma.$transaction([
    prisma.comment.findMany({
      orderBy: { createdAt: "desc" },
      where: { entity, entityId },
      take: limit,
      skip,
    }),
    prisma.comment.count({
      where: { entity, entityId },
    }),
  ])

  const totalPages = Math.ceil(totalCount / limit)

  return {
    data,
    pagination: {
      currentPage: page,
      totalPages,
    },
  }
}

export const getArticles = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    if (!pageIndex) return notFound()
    const limit = dataLimits.articles
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.article.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.article.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)
    if (pageIndex > totalPages) return notFound()

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getBooks = cache(async ({ pageIndex }: { pageIndex: number }) => {
  if (!pageIndex) return notFound()
  const limit = dataLimits.books
  const skip = (pageIndex - 1) * limit

  const [data, totalCount] = await prisma.$transaction([
    prisma.book.findMany({
      orderBy: { createdAt: "desc" },
      take: limit,
      skip,
    }),
    prisma.book.count(),
  ])

  const totalPages = Math.ceil(totalCount / limit)
  if (pageIndex > totalPages) return notFound()

  return {
    data,
    pagination: {
      currentPage: pageIndex,
      totalPages,
    },
  }
})

export const getInterviews = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    if (!pageIndex) return notFound()
    const limit = dataLimits.interviews
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.interview.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.interview.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)
    if (pageIndex > totalPages) return notFound()

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getBlogPosts = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    if (!pageIndex) return notFound()
    const limit = dataLimits.blogPost
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.blogPost.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.blogPost.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getUser = cache(async () => {
  const session = await verifySession()

  if (!session) return null

  const user = await prisma.user.findUnique({
    where: { email: session?.email },
    include: {
      bookOrders: { where: { status: "PAID" }, include: { book: true } },
      courseOrders: { where: { status: "PAID" }, include: { course: true } },
    },
  })

  if (!user) return null

  return user
})

export const getSiteSettings = unstable_cache(
  async (): Promise<SiteSettings> => {
    const siteSettings = await prisma.siteSettings.findUnique({
      where: { id: 1 },
    })

    if (!siteSettings) {
      return {
        id: 1,
        phone: "",
        email: "",
        facebookUrl: "",
        instagramUrl: "",
        twitterUrl: "",
        youtubeUrl: "",
        tiktokUrl: "",
        myStoryVideoUrl: "",
        myStoryVideoThumbnailUrl: "",
        subscriptionInstructions: "",
      }
    }

    return siteSettings
  },
  ["siteSettings"],
  {
    tags: ["siteSettings"],
  }
)

// دالة ترجع العدد الإجمالي لصفحات المدونة
export const getBlogPagesCount = unstable_cache(
  async () => {
    return Math.ceil((await prisma.blogPost.count()) / dataLimits.blogPost)
  },
  ["blogPagesCount"],
  {
    tags: ["blogPagesCount"],
  }
)

// دالة ترجع العدد الإجمالي لصفحات المقالات
export const getArticlesPagesCount = unstable_cache(
  async () => {
    return Math.ceil((await prisma.article.count()) / dataLimits.articles)
  },
  ["articlesPagesCount"],
  {
    tags: ["articlesPagesCount"],
  }
)

// دالة ترجع العدد الإجمالي لصفحات المقابلات
export const getInterviewsPagesCount = unstable_cache(
  async () => {
    return Math.ceil((await prisma.interview.count()) / dataLimits.interviews)
  },
  ["interviewsPagesCount"],
  {
    tags: ["interviewsPagesCount"],
  }
)

// دالة ترجع العدد الإجمالي لصفحات الكتب
export const getBooksPagesCount = unstable_cache(
  async () => {
    return Math.ceil((await prisma.book.count()) / dataLimits.books)
  },
  ["booksPagesCount"],
  {
    tags: ["booksPagesCount"],
  }
)
