import Link from "next/link"
import { Chevron<PERSON>eft, Menu } from "lucide-react"

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  Drawer<PERSON>itle,
  DrawerTrigger,
} from "@/components/ui/drawer"

import { LogoSmall } from "../svg/logos"
import { NavContainer, NavItemContainer, UserAvatar } from "./nav-attachments"
import Search from "../search/search"

export const itemsNav = [
  {
    label: "الرئيسية",
    path: "/",
  },
  {
    label: "كورسات",
    path: "/courses",
  },
  {
    label: "تجربتي الإعلامية",
    path: "/media-experience/articles/1",
  },
  {
    label: "مدونة",
    path: "/blog/1",
  },
  {
    label: "حجز استشارة",
    path: "/consultation",
  },
  // {
  //   label: "نبذة عني",
  //   path: "/about-my",
  // },
  // {
  //   label: "المدونة",
  //   path: "/blog",
  // },
]

export default function Nav() {
  return (
    <NavContainer>
      <NavItemsMd />
      <NavSm />
      <Search />
      <UserAvatar />
    </NavContainer>
  )
}

function NavSm() {
  return (
    <Drawer>
      <DrawerTrigger asChild>
        <div className="flex h-16 items-center px-3 md:hidden">
          <Menu className="text-primary size-7 cursor-pointer" />
        </div>
      </DrawerTrigger>
      <DrawerContent className="bg-background">
        <DrawerHeader className="sr-only">
          <DrawerTitle className="sr-only">القائمة</DrawerTitle>
          <DrawerDescription className="sr-only">
            قائمة التنقل
          </DrawerDescription>
        </DrawerHeader>
        <NavItemsSm />
      </DrawerContent>
    </Drawer>
  )
}

function NavItemsSm() {
  return (
    <div className="flex h-[calc(100vh-200px)] flex-col gap-2 overflow-y-auto px-4 pt-10 pb-6">
      {itemsNav.map(({ label, path }) => (
        <DrawerClose key={path} asChild>
          <Link
            href={path}
            className="flex justify-between rounded-md px-2 py-2 text-base font-bold text-nowrap"
          >
            <NavItemContainer itemPath={path}>{label}</NavItemContainer>
            <ChevronLeft className="text-muted peer-data-[path=true]:text-primary transition-all peer-data-[path=true]:rotate-180 peer-data-[path=true]:drop-shadow-md" />
          </Link>
        </DrawerClose>
      ))}
    </div>
  )
}

function NavItemsMd() {
  return (
    <div className="hidden h-16 flex-1 items-center gap-10 px-6 md:flex">
      <LogoSmall className="hidden h-auto md:block md:w-14" />
      {itemsNav.map(({ label, path }) => (
        <div key={path}>
          <NavItemContainer itemPath={path}>
            <Link
              href={path}
              className="max-w-min text-base font-semibold text-nowrap transition-opacity duration-200 md:text-sm"
            >
              {label}
            </Link>
          </NavItemContainer>
        </div>
      ))}
    </div>
  )
}
